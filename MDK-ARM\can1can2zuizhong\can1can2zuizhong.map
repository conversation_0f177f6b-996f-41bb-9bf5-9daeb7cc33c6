Component: Arm Compiler for Embedded 6.24 Tool: armlink [5f371500]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.CAN2_RX0_IRQHandler) for CAN2_RX0_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(.text.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    main.o(.text.main) refers to stm32f4xx_hal.o(.text.HAL_Init) for HAL_Init
    main.o(.text.main) refers to main.o(.text.SystemClock_Config) for SystemClock_Config
    main.o(.text.main) refers to gpio.o(.text.MX_GPIO_Init) for MX_GPIO_Init
    main.o(.text.main) refers to can.o(.text.MX_CAN1_Init) for MX_CAN1_Init
    main.o(.text.main) refers to can.o(.text.MX_CAN2_Init) for MX_CAN2_Init
    main.o(.text.main) refers to can.o(.text.CAN_FILTER) for CAN_FILTER
    main.o(.text.main) refers to can.o(.bss..L_MergedGlobals) for hcan1
    main.o(.text.main) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_Start) for HAL_CAN_Start
    main.o(.text.main) refers to can.o(.bss.hcan2) for hcan2
    main.o(.text.main) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_ActivateNotification) for HAL_CAN_ActivateNotification
    main.o(.text.main) refers to main.o(.data..L_MergedGlobals) for .L_MergedGlobals
    main.o(.text.main) refers to can.o(.text.can1send) for can1send
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.ARM.exidx.text.SystemClock_Config) refers to main.o(.text.SystemClock_Config) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.Error_Handler) refers to main.o(.text.Error_Handler) for [Anonymous Symbol]
    gpio.o(.ARM.exidx.text.MX_GPIO_Init) refers to gpio.o(.text.MX_GPIO_Init) for [Anonymous Symbol]
    can.o(.text.MX_CAN1_Init) refers to can.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    can.o(.text.MX_CAN1_Init) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_Init) for HAL_CAN_Init
    can.o(.text.MX_CAN1_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    can.o(.ARM.exidx.text.MX_CAN1_Init) refers to can.o(.text.MX_CAN1_Init) for [Anonymous Symbol]
    can.o(.text.MX_CAN2_Init) refers to can.o(.bss.hcan2) for hcan2
    can.o(.text.MX_CAN2_Init) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_Init) for HAL_CAN_Init
    can.o(.text.MX_CAN2_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    can.o(.ARM.exidx.text.MX_CAN2_Init) refers to can.o(.text.MX_CAN2_Init) for [Anonymous Symbol]
    can.o(.text.CAN_FILTER) refers to can.o(.bss.hcan2) for hcan2
    can.o(.text.CAN_FILTER) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_ConfigFilter) for HAL_CAN_ConfigFilter
    can.o(.text.CAN_FILTER) refers to main.o(.text.Error_Handler) for Error_Handler
    can.o(.ARM.exidx.text.CAN_FILTER) refers to can.o(.text.CAN_FILTER) for [Anonymous Symbol]
    can.o(.text.HAL_CAN_MspInit) refers to can.o(.bss.HAL_RCC_CAN1_CLK_ENABLED) for HAL_RCC_CAN1_CLK_ENABLED
    can.o(.text.HAL_CAN_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    can.o(.text.HAL_CAN_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    can.o(.text.HAL_CAN_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    can.o(.ARM.exidx.text.HAL_CAN_MspInit) refers to can.o(.text.HAL_CAN_MspInit) for [Anonymous Symbol]
    can.o(.text.HAL_CAN_MspDeInit) refers to can.o(.bss.HAL_RCC_CAN1_CLK_ENABLED) for HAL_RCC_CAN1_CLK_ENABLED
    can.o(.text.HAL_CAN_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    can.o(.text.HAL_CAN_MspDeInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    can.o(.ARM.exidx.text.HAL_CAN_MspDeInit) refers to can.o(.text.HAL_CAN_MspDeInit) for [Anonymous Symbol]
    can.o(.text.can1send) refers to can.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    can.o(.text.can1send) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_AddTxMessage) for HAL_CAN_AddTxMessage
    can.o(.ARM.exidx.text.can1send) refers to can.o(.text.can1send) for [Anonymous Symbol]
    can.o(.text.HAL_CAN_RxFifo0MsgPendingCallback) refers to can.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    can.o(.text.HAL_CAN_RxFifo0MsgPendingCallback) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_GetRxMessage) for HAL_CAN_GetRxMessage
    can.o(.text.HAL_CAN_RxFifo0MsgPendingCallback) refers to main.o(.text.Error_Handler) for Error_Handler
    can.o(.text.HAL_CAN_RxFifo0MsgPendingCallback) refers to putchar.o(.text) for putchar
    can.o(.ARM.exidx.text.HAL_CAN_RxFifo0MsgPendingCallback) refers to can.o(.text.HAL_CAN_RxFifo0MsgPendingCallback) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f4xx_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f4xx_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32f4xx_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32f4xx_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f4xx_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f4xx_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.SysTick_Handler) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f4xx_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.CAN2_RX0_IRQHandler) refers to can.o(.bss.hcan2) for hcan2
    stm32f4xx_it.o(.text.CAN2_RX0_IRQHandler) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_IRQHandler) for HAL_CAN_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.CAN2_RX0_IRQHandler) refers to stm32f4xx_it.o(.text.CAN2_RX0_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.text.HAL_CAN_Init) refers to can.o(.text.HAL_CAN_MspInit) for HAL_CAN_MspInit
    stm32f4xx_hal_can.o(.text.HAL_CAN_Init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_Init) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_Init) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_MspInit) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.text.HAL_CAN_DeInit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_can.o(.text.HAL_CAN_DeInit) refers to can.o(.text.HAL_CAN_MspDeInit) for HAL_CAN_MspDeInit
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_DeInit) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.text.HAL_CAN_Stop) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_Stop) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_MspDeInit) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_ConfigFilter) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_ConfigFilter) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.text.HAL_CAN_Start) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_Start) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_RequestSleep) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_RequestSleep) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_WakeUp) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_WakeUp) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_IsSleepActive) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_IsSleepActive) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_AddTxMessage) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_AddTxMessage) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_AbortTxRequest) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_AbortTxRequest) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_GetTxMailboxesFreeLevel) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_GetTxMailboxesFreeLevel) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_IsTxMessagePending) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_IsTxMessagePending) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_GetTxTimestamp) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_GetTxTimestamp) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_GetRxMessage) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_GetRxMessage) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_GetRxFifoFillLevel) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_GetRxFifoFillLevel) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_ActivateNotification) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_ActivateNotification) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_DeactivateNotification) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_DeactivateNotification) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.text.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox0AbortCallback) for HAL_CAN_TxMailbox0AbortCallback
    stm32f4xx_hal_can.o(.text.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox0CompleteCallback) for HAL_CAN_TxMailbox0CompleteCallback
    stm32f4xx_hal_can.o(.text.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_RxFifo0FullCallback) for HAL_CAN_RxFifo0FullCallback
    stm32f4xx_hal_can.o(.text.HAL_CAN_IRQHandler) refers to can.o(.text.HAL_CAN_RxFifo0MsgPendingCallback) for HAL_CAN_RxFifo0MsgPendingCallback
    stm32f4xx_hal_can.o(.text.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_RxFifo1FullCallback) for HAL_CAN_RxFifo1FullCallback
    stm32f4xx_hal_can.o(.text.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_RxFifo1MsgPendingCallback) for HAL_CAN_RxFifo1MsgPendingCallback
    stm32f4xx_hal_can.o(.text.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_SleepCallback) for HAL_CAN_SleepCallback
    stm32f4xx_hal_can.o(.text.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_WakeUpFromRxMsgCallback) for HAL_CAN_WakeUpFromRxMsgCallback
    stm32f4xx_hal_can.o(.text.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox1AbortCallback) for HAL_CAN_TxMailbox1AbortCallback
    stm32f4xx_hal_can.o(.text.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox1CompleteCallback) for HAL_CAN_TxMailbox1CompleteCallback
    stm32f4xx_hal_can.o(.text.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox2AbortCallback) for HAL_CAN_TxMailbox2AbortCallback
    stm32f4xx_hal_can.o(.text.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox2CompleteCallback) for HAL_CAN_TxMailbox2CompleteCallback
    stm32f4xx_hal_can.o(.text.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_ErrorCallback) for HAL_CAN_ErrorCallback
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_TxMailbox0CompleteCallback) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox0CompleteCallback) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_TxMailbox0AbortCallback) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox0AbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_TxMailbox1CompleteCallback) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox1CompleteCallback) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_TxMailbox1AbortCallback) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox1AbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_TxMailbox2CompleteCallback) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox2CompleteCallback) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_TxMailbox2AbortCallback) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox2AbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_RxFifo0FullCallback) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_RxFifo0FullCallback) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_RxFifo0MsgPendingCallback) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_RxFifo0MsgPendingCallback) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_RxFifo1FullCallback) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_RxFifo1FullCallback) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_RxFifo1MsgPendingCallback) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_RxFifo1MsgPendingCallback) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_SleepCallback) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_SleepCallback) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_WakeUpFromRxMsgCallback) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_WakeUpFromRxMsgCallback) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_ErrorCallback) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_GetState) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_GetError) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_ResetError) refers to stm32f4xx_hal_can.o(.text.HAL_CAN_ResetError) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.data.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.data.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.data.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.data.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.data.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.data.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.data.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.data.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for __NVIC_SystemReset
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent) refers to stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_Init) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.text.HAL_GetTickPrio) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_SetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_GetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_Delay) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick) refers to stm32f4xx_hal.o(.text.HAL_SuspendTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick) refers to stm32f4xx_hal.o(.text.HAL_ResumeTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion) refers to stm32f4xx_hal.o(.text.HAL_GetHalVersion) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID) refers to stm32f4xx_hal.o(.text.HAL_GetREVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID) refers to stm32f4xx_hal.o(.text.HAL_GetDEVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_EnableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_DisableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw0) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw1) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw2) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI) for [Anonymous Symbol]
    system_stm32f4xx.o(.ARM.exidx.text.SystemInit) refers to system_stm32f4xx.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    putchar.o(.text) refers to fputc.o(i.fputc) for fputc
    putchar.o(.text) refers to stdio_streams.o(.bss) for __stdout
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    fputc.o(i.fputc) refers to flsbuf.o(.text) for __flsbuf_byte
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    flsbuf.o(.text) refers to stdio.o(.text) for _deferredlazyseek
    flsbuf.o(.text) refers to sys_io.o(.text) for _sys_flen
    flsbuf.o(.text) refers to h1_alloc.o(.text) for malloc
    initio.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000026) for __rt_lib_init_stdio_2
    initio.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000003) for __rt_lib_shutdown_stdio_2
    initio.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio.o(.text) refers to fopen.o(.text) for freopen
    initio.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio.o(.text) refers to h1_free.o(.text) for free
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio.o(.text) refers to sys_io_names.o(.constdata) for __stdin_name
    initio.o(.text) refers to sys_io_names.o(.constdata) for __stdout_name
    initio.o(.text) refers to sys_io_names.o(.constdata) for __stderr_name
    initio_locked.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000026) for __rt_lib_init_stdio_2
    initio_locked.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000003) for __rt_lib_shutdown_stdio_2
    initio_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio_locked.o(.text) refers to fopen.o(.text) for freopen
    initio_locked.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio_locked.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio_locked.o(.text) refers to h1_free.o(.text) for free
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    initio_locked.o(.text) refers to sys_io_names.o(.constdata) for __stdin_name
    initio_locked.o(.text) refers to sys_io_names.o(.constdata) for __stdout_name
    initio_locked.o(.text) refers to sys_io_names.o(.constdata) for __stderr_name
    flsbuf_fwide.o(.text) refers to flsbuf.o(.text) for __flsbuf
    sys_io.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.text) refers to strlen.o(.text) for strlen
    sys_io_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io_hlt.o(.text) refers to strlen.o(.text) for strlen
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    malloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    malloc.o(.text) refers (Special) to init_alloc.o(.text) for _init_alloc
    malloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    malloc.o(.text) refers to heapstubs.o(.text) for __Heap_Alloc
    free.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    free.o(.text) refers to heapstubs.o(.text) for __Heap_Free
    h1_alloc.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc_threads.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc_threads.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_threads.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_free_threads.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._FDIterate) refers to heap2.o(.conststring) for .conststring
    heap2.o(i.__Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i.__Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.__Heap_Stats$realtime) refers to heap2.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2.o(i.__Heap_Valid$realtime) refers to heap2.o(i._FDIterate) for _FDIterate
    heap2.o(i.__Heap_Valid$realtime) refers to heap2.o(.conststring) for .conststring
    heap2.o(i.free$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i.free$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i.free$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.malloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i.malloc$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i.malloc$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i.malloc$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i.malloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.posix_memalign$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i.posix_memalign$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i.posix_memalign$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i.posix_memalign$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i.posix_memalign$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.realloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i.realloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.realloc$realtime) refers to h1_free.o(.text) for free
    heap2.o(i.realloc$realtime) refers to h1_alloc.o(.text) for malloc
    heap2.o(i.realloc$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i.realloc$realtime) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    heap2_threads.o(i._FDIterate) refers to heap2_threads.o(.conststring) for .conststring
    heap2_threads.o(i.__Heap_Initialize$realtime$concurrent) refers to mutex_dummy.o(.text) for _mutex_initialize
    heap2_threads.o(i.__Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2_threads.o(i.__Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2_threads.o(i.__Heap_Stats$realtime$concurrent) refers to heap2_threads.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2_threads.o(i.__Heap_Valid$realtime$concurrent) refers to heap2_threads.o(i._FDIterate) for _FDIterate
    heap2_threads.o(i.__Heap_Valid$realtime$concurrent) refers to heap2_threads.o(.conststring) for .conststring
    heap2_threads.o(i.free$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2_threads.o(i.free$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2_threads.o(i.free$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2_threads.o(i.malloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2_threads.o(i.malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2_threads.o(i.malloc$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2_threads.o(i.malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2_threads.o(i.malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2_threads.o(i.posix_memalign$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2_threads.o(i.posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2_threads.o(i.posix_memalign$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2_threads.o(i.posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2_threads.o(i.posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2_threads.o(i.realloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2_threads.o(i.realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2_threads.o(i.realloc$realtime$concurrent) refers to h1_free.o(.text) for free
    heap2_threads.o(i.realloc$realtime$concurrent) refers to h1_alloc.o(.text) for malloc
    heap2_threads.o(i.realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2_threads.o(i.realloc$realtime$concurrent) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    stdio.o(.text) refers to sys_io.o(.text) for _sys_seek
    fwritefast.o(.text) refers to stdio.o(.text) for _writebuf
    fwritefast.o(.text) refers to flsbuf.o(.text) for __flsbuf_byte
    fwritefast.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    streamlock.o(.data) refers (Special) to initio.o(.text) for _initio
    fopen.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen.o(.text) refers to fseek.o(.text) for _fseek
    fopen.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen.o(.text) refers to stdio_streams.o(.bss) for __stdin
    fclose.o(.text) refers to stdio.o(.text) for _fflush
    fclose.o(.text) refers to sys_io.o(.text) for _sys_close
    fclose.o(.text) refers to h1_free.o(.text) for free
    fclose.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen_locked.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen_locked.o(.text) refers to fseek.o(.text) for _fseek
    fopen_locked.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    fopen_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    fwritefast_locked.o(.text) refers to stdio.o(.text) for _writebuf
    fwritefast_locked.o(.text) refers to flsbuf.o(.text) for __flsbuf_byte
    fwritefast_locked.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_rtred_outer.o(.text) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig_rtred_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtred_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    assert_stdio.o(.text) refers to fputs.o(.text) for fputs
    assert_stdio.o(.text) refers to fflush.o(.text) for fflush
    assert_stdio.o(.text) refers to stdio_streams.o(.bss) for __stderr
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000007) refers (Weak) to init_alloc.o(.text) for _init_alloc
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers (Weak) to initio.o(.text) for _initio
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libshutdown2.o(.ARM.Collect$$libshutdown$$00000003) refers (Weak) to initio.o(.text) for _terminateio
    libshutdown2.o(.ARM.Collect$$libshutdown$$0000000D) refers (Weak) to term_alloc.o(.text) for _terminate_alloc
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    rt_heap_descriptor.o(.text) refers to rt_heap_descriptor.o(.bss) for __rt_heap_descriptor_data
    rt_heap_descriptor_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    init_alloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    init_alloc.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000007) for __rt_lib_init_heap_2
    init_alloc.o(.text) refers (Special) to maybetermalloc1.o(.text) for _maybe_terminate_alloc
    init_alloc.o(.text) refers to h1_extend.o(.text) for __Heap_ProvideMemory
    init_alloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    init_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    init_alloc.o(.text) refers to h1_init.o(.text) for __Heap_Initialize
    h1_init_threads.o(.text) refers to mutex_dummy.o(.text) for _mutex_initialize
    fflush.o(.text) refers to stdio.o(.text) for _fflush
    fflush.o(.text) refers to fseek.o(.text) for _fseek
    fflush.o(.text) refers to stdio_streams.o(.bss) for __stdin
    fputs.o(.text) refers to fputc.o(i.fputc) for fputc
    fseek.o(.text) refers to sys_io.o(.text) for _sys_istty
    fseek.o(.text) refers to ftell.o(.text) for _ftell_internal
    fseek.o(.text) refers to stdio.o(.text) for _seterr
    fflush_locked.o(.text) refers to stdio.o(.text) for _fflush
    fflush_locked.o(.text) refers to fseek.o(.text) for _fseek
    fflush_locked.o(.text) refers to fflush.o(.text) for _do_fflush
    fflush_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    fflush_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    _get_argv.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv.o(.text) refers to h1_alloc.o(.text) for malloc
    _get_argv.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv.o(.text) refers to sys_command.o(.text) for _sys_command_string
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    maybetermalloc2.o(.text) refers (Special) to term_alloc.o(.text) for _terminate_alloc
    h1_extend.o(.text) refers to h1_free.o(.text) for free
    h1_extend_threads.o(.text) refers to h1_free_threads.o(.text) for free_internal$concurrent
    ftell.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    heapauxa.o(.text) refers to heapauxa.o(.data) for .data
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    term_alloc.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_heap_2
    term_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    term_alloc.o(.text) refers to h1_final.o(.text) for __Heap_Finalize
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.exidx.text.SystemClock_Config), (8 bytes).
    Removing main.o(.ARM.exidx.text.Error_Handler), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing gpio.o(.text), (0 bytes).
    Removing gpio.o(.ARM.exidx.text.MX_GPIO_Init), (8 bytes).
    Removing can.o(.text), (0 bytes).
    Removing can.o(.ARM.exidx.text.MX_CAN1_Init), (8 bytes).
    Removing can.o(.ARM.exidx.text.MX_CAN2_Init), (8 bytes).
    Removing can.o(.ARM.exidx.text.CAN_FILTER), (8 bytes).
    Removing can.o(.ARM.exidx.text.HAL_CAN_MspInit), (8 bytes).
    Removing can.o(.text.HAL_CAN_MspDeInit), (158 bytes).
    Removing can.o(.ARM.exidx.text.HAL_CAN_MspDeInit), (8 bytes).
    Removing can.o(.ARM.exidx.text.can1send), (8 bytes).
    Removing can.o(.ARM.exidx.text.HAL_CAN_RxFifo0MsgPendingCallback), (8 bytes).
    Removing stm32f4xx_it.o(.text), (0 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.CAN2_RX0_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_msp.o(.text), (0 bytes).
    Removing stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal_can.o(.text), (0 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_Init), (8 bytes).
    Removing stm32f4xx_hal_can.o(.text.HAL_CAN_MspInit), (2 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_MspInit), (8 bytes).
    Removing stm32f4xx_hal_can.o(.text.HAL_CAN_DeInit), (120 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_DeInit), (8 bytes).
    Removing stm32f4xx_hal_can.o(.text.HAL_CAN_Stop), (94 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_Stop), (8 bytes).
    Removing stm32f4xx_hal_can.o(.text.HAL_CAN_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_ConfigFilter), (8 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_Start), (8 bytes).
    Removing stm32f4xx_hal_can.o(.text.HAL_CAN_RequestSleep), (42 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_RequestSleep), (8 bytes).
    Removing stm32f4xx_hal_can.o(.text.HAL_CAN_WakeUp), (136 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_WakeUp), (8 bytes).
    Removing stm32f4xx_hal_can.o(.text.HAL_CAN_IsSleepActive), (24 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_IsSleepActive), (8 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_AddTxMessage), (8 bytes).
    Removing stm32f4xx_hal_can.o(.text.HAL_CAN_AbortTxRequest), (86 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_AbortTxRequest), (8 bytes).
    Removing stm32f4xx_hal_can.o(.text.HAL_CAN_GetTxMailboxesFreeLevel), (40 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_GetTxMailboxesFreeLevel), (8 bytes).
    Removing stm32f4xx_hal_can.o(.text.HAL_CAN_IsTxMessagePending), (32 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_IsTxMessagePending), (8 bytes).
    Removing stm32f4xx_hal_can.o(.text.HAL_CAN_GetTxTimestamp), (36 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_GetTxTimestamp), (8 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_GetRxMessage), (8 bytes).
    Removing stm32f4xx_hal_can.o(.text.HAL_CAN_GetRxFifoFillLevel), (32 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_GetRxFifoFillLevel), (8 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_ActivateNotification), (8 bytes).
    Removing stm32f4xx_hal_can.o(.text.HAL_CAN_DeactivateNotification), (36 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_DeactivateNotification), (8 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_TxMailbox0CompleteCallback), (8 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_TxMailbox0AbortCallback), (8 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_TxMailbox1CompleteCallback), (8 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_TxMailbox1AbortCallback), (8 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_TxMailbox2CompleteCallback), (8 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_TxMailbox2AbortCallback), (8 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_RxFifo0FullCallback), (8 bytes).
    Removing stm32f4xx_hal_can.o(.text.HAL_CAN_RxFifo0MsgPendingCallback), (2 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_RxFifo0MsgPendingCallback), (8 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_RxFifo1FullCallback), (8 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_RxFifo1MsgPendingCallback), (8 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_SleepCallback), (8 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_WakeUpFromRxMsgCallback), (8 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_can.o(.text.HAL_CAN_GetState), (38 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_GetState), (8 bytes).
    Removing stm32f4xx_hal_can.o(.text.HAL_CAN_GetError), (4 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_GetError), (8 bytes).
    Removing stm32f4xx_hal_can.o(.text.HAL_CAN_ResetError), (32 bytes).
    Removing stm32f4xx_hal_can.o(.ARM.exidx.text.HAL_CAN_ResetError), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig), (168 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq), (38 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq), (38 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig), (150 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig), (62 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig), (330 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig), (50 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq), (88 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S), (106 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit), (296 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program), (218 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation), (250 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT), (240 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler), (312 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock), (46 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock), (42 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.data.pFlash), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase), (394 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector), (70 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT), (176 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram), (184 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig), (56 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig), (8 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit), (466 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin), (10 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin), (44 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler), (22 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart), (114 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT), (1236 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory), (20 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Init), (354 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit), (136 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Start), (114 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT), (162 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort), (142 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT), (36 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer), (454 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler), (452 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback), (50 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback), (156 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.rodata.cst8), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit), (26 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD), (130 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode), (84 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling), (102 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion), (94 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority), (88 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback), (8 bytes).
    Removing stm32f4xx_hal.o(.text), (0 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DeInit), (68 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SetTickFreq), (38 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_Delay), (40 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetHalVersion), (10 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetREVID), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_EnableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DisableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text), (0 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine), (186 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine), (148 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine), (140 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback), (12 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler), (42 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending), (26 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI), (8 bytes).
    Removing system_stm32f4xx.o(.text), (0 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f4xx.o(.text.SystemCoreClockUpdate), (134 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing system_stm32f4xx.o(.rodata.APBPrescTable), (8 bytes).

375 unused section(s) (total 12720 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  mutex_dummy.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_io.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_io_names.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_io_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/assert.c                         0x00000000   Number         0  assert_stdio.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc_threads.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free_threads.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init_threads.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend_threads.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final_threads.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2_threads.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  fdtree.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  free.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  init_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  heapstubs.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  term_alloc.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxa.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/maybe.s                          0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/maybe.s                          0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  putchar.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  stdio_streams.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputc_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  flsbuf.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  initio.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  initio_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  setvbuf.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  stdio.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fwritefast.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  streamlock.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fopen.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fclose.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  setvbuf_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fopen_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fwritefast_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fflush.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputs.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fseek.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputs_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fflush_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ftell.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/wchar.c                          0x00000000   Number         0  flsbuf_fwide.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    can.c                                    0x00000000   Number         0  can.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    gpio.c                                   0x00000000   Number         0  gpio.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    stm32f4xx_hal.c                          0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    stm32f4xx_hal_can.c                      0x00000000   Number         0  stm32f4xx_hal_can.o ABSOLUTE
    stm32f4xx_hal_cortex.c                   0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    stm32f4xx_hal_dma.c                      0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    stm32f4xx_hal_dma_ex.c                   0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    stm32f4xx_hal_exti.c                     0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    stm32f4xx_hal_flash.c                    0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    stm32f4xx_hal_flash_ex.c                 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    stm32f4xx_hal_flash_ramfunc.c            0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    stm32f4xx_hal_gpio.c                     0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    stm32f4xx_hal_msp.c                      0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    stm32f4xx_hal_pwr.c                      0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    stm32f4xx_hal_pwr_ex.c                   0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    stm32f4xx_hal_rcc.c                      0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    stm32f4xx_hal_rcc_ex.c                   0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       92  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001ec   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_null                           0x08000208   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x0800020c   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x08000228   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800022a   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$00000007          0x0800022e   Section        8  libinit2.o(.ARM.Collect$$libinit$$00000007)
    .ARM.Collect$$libinit$$0000000C          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000013          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000026          0x08000236   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000026)
    .ARM.Collect$$libinit$$00000027          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x0800023a   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x0800023c   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800023e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000003      0x0800023e   Section        4  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    .ARM.Collect$$libshutdown$$00000004      0x08000242   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000242   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000242   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000242   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000242   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000242   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000244   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000244   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000244   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800024a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800024a   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800024e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800024e   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000256   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000258   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000258   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800025c   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    $v0                                      0x08000264   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x08000264   Section       64  startup_stm32f407xx.o(.text)
    .text                                    0x080002a4   Section      240  lludivv7m.o(.text)
    .text                                    0x08000394   Section        0  putchar.o(.text)
    .text                                    0x080003a0   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080003ee   Section        0  heapauxi.o(.text)
    .text                                    0x080003f4   Section        0  flsbuf.o(.text)
    .text                                    0x080005d8   Section        0  initio.o(.text)
    .text                                    0x08000710   Section        0  sys_io.o(.text)
    .text                                    0x08000776   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080007c0   Section        0  h1_alloc.o(.text)
    .text                                    0x0800081e   Section        0  h1_free.o(.text)
    .text                                    0x0800086c   Section        0  setvbuf.o(.text)
    .text                                    0x080008b4   Section        0  stdio.o(.text)
    _freopen_locked                          0x080009bd   Thumb Code     0  fopen.o(.text)
    .text                                    0x080009bc   Section        0  fopen.o(.text)
    .text                                    0x08000aac   Section        0  fclose.o(.text)
    .text                                    0x08000af8   Section        0  exit.o(.text)
    .text                                    0x08000b0a   Section        0  defsig_rtred_outer.o(.text)
    .text                                    0x08000b18   Section        8  libspace.o(.text)
    .text                                    0x08000b20   Section        2  use_no_semi.o(.text)
    .text                                    0x08000b22   Section        0  indicate_semi.o(.text)
    .text                                    0x08000b24   Section        8  rt_heap_descriptor_intlibspace.o(.text)
    .text                                    0x08000b2c   Section        0  hguard.o(.text)
    .text                                    0x08000b30   Section        0  init_alloc.o(.text)
    .text                                    0x08000bba   Section        0  h1_init.o(.text)
    .text                                    0x08000bc8   Section        0  fseek.o(.text)
    .text                                    0x08000cc0   Section        0  defsig_exit.o(.text)
    .text                                    0x08000ccc   Section        0  defsig_rtred_inner.o(.text)
    .text                                    0x08000d00   Section        0  strlen.o(.text)
    .text                                    0x08000d40   Section        0  sys_exit.o(.text)
    .text                                    0x08000d4c   Section        0  maybetermalloc1.o(.text)
    .text                                    0x08000d4c   Section        0  h1_extend.o(.text)
    .text                                    0x08000d80   Section        0  ftell.o(.text)
    .text                                    0x08000dc2   Section        0  defsig_general.o(.text)
    .text                                    0x08000df4   Section        0  defsig_rtmem_outer.o(.text)
    .text                                    0x08000e02   Section        0  sys_wrch.o(.text)
    .text                                    0x08000e10   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000e18   Section        0  defsig_rtmem_inner.o(.text)
    [Anonymous Symbol]                       0x08000e68   Section        0  stm32f4xx_it.o(.text.BusFault_Handler)
    [Anonymous Symbol]                       0x08000e6c   Section        0  stm32f4xx_it.o(.text.CAN2_RX0_IRQHandler)
    [Anonymous Symbol]                       0x08000e78   Section        0  can.o(.text.CAN_FILTER)
    [Anonymous Symbol]                       0x08000eb0   Section        0  stm32f4xx_it.o(.text.DebugMon_Handler)
    [Anonymous Symbol]                       0x08000eb4   Section        0  main.o(.text.Error_Handler)
    [Anonymous Symbol]                       0x08000ebc   Section        0  stm32f4xx_hal_can.o(.text.HAL_CAN_ActivateNotification)
    [Anonymous Symbol]                       0x08000eec   Section        0  stm32f4xx_hal_can.o(.text.HAL_CAN_AddTxMessage)
    [Anonymous Symbol]                       0x08000f90   Section        0  stm32f4xx_hal_can.o(.text.HAL_CAN_ConfigFilter)
    [Anonymous Symbol]                       0x08001070   Section        0  stm32f4xx_hal_can.o(.text.HAL_CAN_ErrorCallback)
    [Anonymous Symbol]                       0x08001074   Section        0  stm32f4xx_hal_can.o(.text.HAL_CAN_GetRxMessage)
    [Anonymous Symbol]                       0x080011a4   Section        0  stm32f4xx_hal_can.o(.text.HAL_CAN_IRQHandler)
    [Anonymous Symbol]                       0x08001404   Section        0  stm32f4xx_hal_can.o(.text.HAL_CAN_Init)
    [Anonymous Symbol]                       0x080014f8   Section        0  can.o(.text.HAL_CAN_MspInit)
    [Anonymous Symbol]                       0x08001628   Section        0  stm32f4xx_hal_can.o(.text.HAL_CAN_RxFifo0FullCallback)
    [Anonymous Symbol]                       0x0800162c   Section        0  can.o(.text.HAL_CAN_RxFifo0MsgPendingCallback)
    [Anonymous Symbol]                       0x08001664   Section        0  stm32f4xx_hal_can.o(.text.HAL_CAN_RxFifo1FullCallback)
    [Anonymous Symbol]                       0x08001668   Section        0  stm32f4xx_hal_can.o(.text.HAL_CAN_RxFifo1MsgPendingCallback)
    [Anonymous Symbol]                       0x0800166c   Section        0  stm32f4xx_hal_can.o(.text.HAL_CAN_SleepCallback)
    [Anonymous Symbol]                       0x08001670   Section        0  stm32f4xx_hal_can.o(.text.HAL_CAN_Start)
    [Anonymous Symbol]                       0x080016cc   Section        0  stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox0AbortCallback)
    [Anonymous Symbol]                       0x080016d0   Section        0  stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox0CompleteCallback)
    [Anonymous Symbol]                       0x080016d4   Section        0  stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox1AbortCallback)
    [Anonymous Symbol]                       0x080016d8   Section        0  stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox1CompleteCallback)
    [Anonymous Symbol]                       0x080016dc   Section        0  stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox2AbortCallback)
    [Anonymous Symbol]                       0x080016e0   Section        0  stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox2CompleteCallback)
    [Anonymous Symbol]                       0x080016e4   Section        0  stm32f4xx_hal_can.o(.text.HAL_CAN_WakeUpFromRxMsgCallback)
    [Anonymous Symbol]                       0x080016e8   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    [Anonymous Symbol]                       0x08001888   Section        0  stm32f4xx_hal.o(.text.HAL_GetTick)
    [Anonymous Symbol]                       0x08001894   Section        0  stm32f4xx_hal.o(.text.HAL_IncTick)
    [Anonymous Symbol]                       0x080018b0   Section        0  stm32f4xx_hal.o(.text.HAL_Init)
    [Anonymous Symbol]                       0x080018e8   Section        0  stm32f4xx_hal.o(.text.HAL_InitTick)
    [Anonymous Symbol]                       0x08001930   Section        0  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    [Anonymous Symbol]                       0x08001968   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x0800198c   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    [Anonymous Symbol]                       0x080019e4   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    [Anonymous Symbol]                       0x08001a04   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    [Anonymous Symbol]                       0x08001b68   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    [Anonymous Symbol]                       0x08001bd8   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    [Anonymous Symbol]                       0x08001f84   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    [Anonymous Symbol]                       0x08001fb0   Section        0  stm32f4xx_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x08001fb4   Section        0  can.o(.text.MX_CAN1_Init)
    [Anonymous Symbol]                       0x08001ff4   Section        0  can.o(.text.MX_CAN2_Init)
    [Anonymous Symbol]                       0x08002030   Section        0  gpio.o(.text.MX_GPIO_Init)
    [Anonymous Symbol]                       0x08002090   Section        0  stm32f4xx_it.o(.text.MemManage_Handler)
    [Anonymous Symbol]                       0x08002094   Section        0  stm32f4xx_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x08002098   Section        0  stm32f4xx_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x0800209c   Section        0  stm32f4xx_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x080020a0   Section        0  stm32f4xx_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x080020a4   Section        0  main.o(.text.SystemClock_Config)
    [Anonymous Symbol]                       0x08002148   Section        0  system_stm32f4xx.o(.text.SystemInit)
    [Anonymous Symbol]                       0x0800215c   Section        0  stm32f4xx_it.o(.text.UsageFault_Handler)
    [Anonymous Symbol]                       0x08002160   Section        0  can.o(.text.can1send)
    [Anonymous Symbol]                       0x08002190   Section        0  main.o(.text.main)
    i.fputc                                  0x080021e2   Section        0  fputc.o(i.fputc)
    $v0                                      0x080021fe   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fpinit                             0x080021fe   Section       26  fpinit.o(x$fpl$fpinit)
    .constdata                               0x08002218   Section        4  sys_io_names.o(.constdata)
    .constdata                               0x0800221c   Section        4  sys_io_names.o(.constdata)
    .constdata                               0x08002220   Section        4  sys_io_names.o(.constdata)
    .data                                    0x20000000   Section        4  stdio_streams.o(.data)
    .data                                    0x20000004   Section        4  stdio_streams.o(.data)
    .data                                    0x20000008   Section        4  stdio_streams.o(.data)
    .L_MergedGlobals                         0x2000000c   Data          10  main.o(.data..L_MergedGlobals)
    [Anonymous Symbol]                       0x2000000c   Section        0  main.o(.data..L_MergedGlobals)
    .L_MergedGlobals                         0x20000018   Data           8  stm32f4xx_hal.o(.data..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000018   Section        0  stm32f4xx_hal.o(.data..L_MergedGlobals)
    .bss                                     0x20000028   Section       84  stdio_streams.o(.bss)
    .bss                                     0x2000007c   Section       84  stdio_streams.o(.bss)
    .bss                                     0x200000d0   Section       84  stdio_streams.o(.bss)
    .bss                                     0x20000124   Section       96  libspace.o(.bss)
    .L_MergedGlobals                         0x20000184   Data         104  can.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000184   Section        0  can.o(.bss..L_MergedGlobals)
    HAL_RCC_CAN1_CLK_ENABLED                 0x200001ec   Data           4  can.o(.bss.HAL_RCC_CAN1_CLK_ENABLED)
    [Anonymous Symbol]                       0x200001ec   Section        0  can.o(.bss.HAL_RCC_CAN1_CLK_ENABLED)
    Heap_Mem                                 0x20000220   Data         512  startup_stm32f407xx.o(HEAP)
    HEAP                                     0x20000220   Section      512  startup_stm32f407xx.o(HEAP)
    Stack_Mem                                0x20000420   Data        1024  startup_stm32f407xx.o(STACK)
    STACK                                    0x20000420   Section     1024  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x20000820   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    __user_heap_extent                        - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_free                               - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    84  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x0800019b   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001ed   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_null                       0x08000209   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x0800020d   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x08000229   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800022b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_2                     0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000007)
    __rt_lib_init_preinit_1                  0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_relocate_pie_1             0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_atexit_1                   0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_fp_trap_1                  0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_heap_1                     0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_1                 0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_1               0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_rand_1                     0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_signal_1                   0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_2                    0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000026)
    __rt_lib_init_user_alloc_1               0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_alloca_1                   0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_cpp_1                      0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_return                     0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_stdio_1                    0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_shutdown                        0x0800023d   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800023f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_stdio_2                0x0800023f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    __rt_lib_shutdown_fp_trap_1              0x08000243   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000243   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000243   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000243   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000243   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000243   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000245   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000245   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000245   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800024b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800024b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800024f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800024f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000257   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000259   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000259   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800025d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000265   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART1_IRQHandler                        0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x080002a5   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x080002a5   Thumb Code   240  lludivv7m.o(.text)
    putchar                                  0x08000395   Thumb Code     6  putchar.o(.text)
    __aeabi_memclr4                          0x080003a1   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080003a1   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080003a1   Thumb Code     0  rt_memclr_w.o(.text)
    _memset_w                                0x080003a5   Thumb Code    74  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x080003ef   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow                         0x080003f1   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand                         0x080003f3   Thumb Code     2  heapauxi.o(.text)
    __flsbuf                                 0x080003f5   Thumb Code   482  flsbuf.o(.text)
    __flsbuf_byte                            0x080003f5   Thumb Code     0  flsbuf.o(.text)
    __flsbuf_wide                            0x080003f5   Thumb Code     0  flsbuf.o(.text)
    _initio                                  0x080005d9   Thumb Code   210  initio.o(.text)
    _terminateio                             0x080006ab   Thumb Code    56  initio.o(.text)
    _sys_open                                0x08000711   Thumb Code    20  sys_io.o(.text)
    _sys_close                               0x08000725   Thumb Code    12  sys_io.o(.text)
    _sys_write                               0x08000731   Thumb Code    16  sys_io.o(.text)
    _sys_read                                0x08000741   Thumb Code    14  sys_io.o(.text)
    _sys_istty                               0x0800074f   Thumb Code    12  sys_io.o(.text)
    _sys_seek                                0x0800075b   Thumb Code    14  sys_io.o(.text)
    _sys_ensure                              0x08000769   Thumb Code     2  sys_io.o(.text)
    _sys_flen                                0x0800076b   Thumb Code    12  sys_io.o(.text)
    __user_setup_stackheap                   0x08000777   Thumb Code    74  sys_stackheap_outer.o(.text)
    malloc                                   0x080007c1   Thumb Code    94  h1_alloc.o(.text)
    free                                     0x0800081f   Thumb Code    78  h1_free.o(.text)
    setvbuf                                  0x0800086d   Thumb Code    70  setvbuf.o(.text)
    _seterr                                  0x080008b5   Thumb Code    20  stdio.o(.text)
    _writebuf                                0x080008c9   Thumb Code   108  stdio.o(.text)
    _fflush                                  0x08000935   Thumb Code    70  stdio.o(.text)
    _deferredlazyseek                        0x0800097b   Thumb Code    60  stdio.o(.text)
    freopen                                  0x080009bd   Thumb Code   160  fopen.o(.text)
    fopen                                    0x08000a5d   Thumb Code    74  fopen.o(.text)
    _fclose_internal                         0x08000aad   Thumb Code    76  fclose.o(.text)
    fclose                                   0x08000aad   Thumb Code     0  fclose.o(.text)
    exit                                     0x08000af9   Thumb Code    18  exit.o(.text)
    __rt_SIGRTRED                            0x08000b0b   Thumb Code    14  defsig_rtred_outer.o(.text)
    __user_libspace                          0x08000b19   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000b19   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000b19   Thumb Code     0  libspace.o(.text)
    __I$use$semihosting                      0x08000b21   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000b21   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08000b23   Thumb Code     0  indicate_semi.o(.text)
    __rt_heap_descriptor                     0x08000b25   Thumb Code     8  rt_heap_descriptor_intlibspace.o(.text)
    __use_no_heap                            0x08000b2d   Thumb Code     2  hguard.o(.text)
    __heap$guard                             0x08000b2f   Thumb Code     2  hguard.o(.text)
    _terminate_user_alloc                    0x08000b31   Thumb Code     2  init_alloc.o(.text)
    _init_user_alloc                         0x08000b33   Thumb Code     2  init_alloc.o(.text)
    __Heap_Full                              0x08000b35   Thumb Code    34  init_alloc.o(.text)
    __Heap_Broken                            0x08000b57   Thumb Code     6  init_alloc.o(.text)
    _init_alloc                              0x08000b5d   Thumb Code    94  init_alloc.o(.text)
    __Heap_Initialize                        0x08000bbb   Thumb Code     8  h1_init.o(.text)
    __Heap_DescSize                          0x08000bc3   Thumb Code     4  h1_init.o(.text)
    _fseek                                   0x08000bc9   Thumb Code   242  fseek.o(.text)
    fseek                                    0x08000bc9   Thumb Code     0  fseek.o(.text)
    __sig_exit                               0x08000cc1   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGRTRED_inner                      0x08000ccd   Thumb Code    14  defsig_rtred_inner.o(.text)
    strlen                                   0x08000d01   Thumb Code    62  strlen.o(.text)
    _sys_exit                                0x08000d41   Thumb Code     8  sys_exit.o(.text)
    __Heap_ProvideMemory                     0x08000d4d   Thumb Code    52  h1_extend.o(.text)
    _maybe_terminate_alloc                   0x08000d4d   Thumb Code     0  maybetermalloc1.o(.text)
    _ftell_internal                          0x08000d81   Thumb Code    66  ftell.o(.text)
    ftell                                    0x08000d81   Thumb Code     0  ftell.o(.text)
    __default_signal_display                 0x08000dc3   Thumb Code    50  defsig_general.o(.text)
    __rt_SIGRTMEM                            0x08000df5   Thumb Code    14  defsig_rtmem_outer.o(.text)
    _ttywrch                                 0x08000e03   Thumb Code    14  sys_wrch.o(.text)
    __aeabi_errno_addr                       0x08000e11   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000e11   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000e11   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_SIGRTMEM_inner                      0x08000e19   Thumb Code    22  defsig_rtmem_inner.o(.text)
    BusFault_Handler                         0x08000e69   Thumb Code     2  stm32f4xx_it.o(.text.BusFault_Handler)
    CAN2_RX0_IRQHandler                      0x08000e6d   Thumb Code    12  stm32f4xx_it.o(.text.CAN2_RX0_IRQHandler)
    CAN_FILTER                               0x08000e79   Thumb Code    54  can.o(.text.CAN_FILTER)
    DebugMon_Handler                         0x08000eb1   Thumb Code     2  stm32f4xx_it.o(.text.DebugMon_Handler)
    Error_Handler                            0x08000eb5   Thumb Code     6  main.o(.text.Error_Handler)
    HAL_CAN_ActivateNotification             0x08000ebd   Thumb Code    46  stm32f4xx_hal_can.o(.text.HAL_CAN_ActivateNotification)
    HAL_CAN_AddTxMessage                     0x08000eed   Thumb Code   164  stm32f4xx_hal_can.o(.text.HAL_CAN_AddTxMessage)
    HAL_CAN_ConfigFilter                     0x08000f91   Thumb Code   222  stm32f4xx_hal_can.o(.text.HAL_CAN_ConfigFilter)
    HAL_CAN_ErrorCallback                    0x08001071   Thumb Code     2  stm32f4xx_hal_can.o(.text.HAL_CAN_ErrorCallback)
    HAL_CAN_GetRxMessage                     0x08001075   Thumb Code   302  stm32f4xx_hal_can.o(.text.HAL_CAN_GetRxMessage)
    HAL_CAN_IRQHandler                       0x080011a5   Thumb Code   606  stm32f4xx_hal_can.o(.text.HAL_CAN_IRQHandler)
    HAL_CAN_Init                             0x08001405   Thumb Code   244  stm32f4xx_hal_can.o(.text.HAL_CAN_Init)
    HAL_CAN_MspInit                          0x080014f9   Thumb Code   304  can.o(.text.HAL_CAN_MspInit)
    HAL_CAN_RxFifo0FullCallback              0x08001629   Thumb Code     2  stm32f4xx_hal_can.o(.text.HAL_CAN_RxFifo0FullCallback)
    HAL_CAN_RxFifo0MsgPendingCallback        0x0800162d   Thumb Code    56  can.o(.text.HAL_CAN_RxFifo0MsgPendingCallback)
    HAL_CAN_RxFifo1FullCallback              0x08001665   Thumb Code     2  stm32f4xx_hal_can.o(.text.HAL_CAN_RxFifo1FullCallback)
    HAL_CAN_RxFifo1MsgPendingCallback        0x08001669   Thumb Code     2  stm32f4xx_hal_can.o(.text.HAL_CAN_RxFifo1MsgPendingCallback)
    HAL_CAN_SleepCallback                    0x0800166d   Thumb Code     2  stm32f4xx_hal_can.o(.text.HAL_CAN_SleepCallback)
    HAL_CAN_Start                            0x08001671   Thumb Code    90  stm32f4xx_hal_can.o(.text.HAL_CAN_Start)
    HAL_CAN_TxMailbox0AbortCallback          0x080016cd   Thumb Code     2  stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox0AbortCallback)
    HAL_CAN_TxMailbox0CompleteCallback       0x080016d1   Thumb Code     2  stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox0CompleteCallback)
    HAL_CAN_TxMailbox1AbortCallback          0x080016d5   Thumb Code     2  stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox1AbortCallback)
    HAL_CAN_TxMailbox1CompleteCallback       0x080016d9   Thumb Code     2  stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox1CompleteCallback)
    HAL_CAN_TxMailbox2AbortCallback          0x080016dd   Thumb Code     2  stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox2AbortCallback)
    HAL_CAN_TxMailbox2CompleteCallback       0x080016e1   Thumb Code     2  stm32f4xx_hal_can.o(.text.HAL_CAN_TxMailbox2CompleteCallback)
    HAL_CAN_WakeUpFromRxMsgCallback          0x080016e5   Thumb Code     2  stm32f4xx_hal_can.o(.text.HAL_CAN_WakeUpFromRxMsgCallback)
    HAL_GPIO_Init                            0x080016e9   Thumb Code   414  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    HAL_GetTick                              0x08001889   Thumb Code    12  stm32f4xx_hal.o(.text.HAL_GetTick)
    HAL_IncTick                              0x08001895   Thumb Code    26  stm32f4xx_hal.o(.text.HAL_IncTick)
    HAL_Init                                 0x080018b1   Thumb Code    54  stm32f4xx_hal.o(.text.HAL_Init)
    HAL_InitTick                             0x080018e9   Thumb Code    72  stm32f4xx_hal.o(.text.HAL_InitTick)
    HAL_MspInit                              0x08001931   Thumb Code    56  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08001969   Thumb Code    34  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x0800198d   Thumb Code    86  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x080019e5   Thumb Code    32  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08001a05   Thumb Code   356  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    HAL_RCC_GetSysClockFreq                  0x08001b69   Thumb Code   110  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001bd9   Thumb Code   940  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08001f85   Thumb Code    44  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    HardFault_Handler                        0x08001fb1   Thumb Code     2  stm32f4xx_it.o(.text.HardFault_Handler)
    MX_CAN1_Init                             0x08001fb5   Thumb Code    62  can.o(.text.MX_CAN1_Init)
    MX_CAN2_Init                             0x08001ff5   Thumb Code    58  can.o(.text.MX_CAN2_Init)
    MX_GPIO_Init                             0x08002031   Thumb Code    96  gpio.o(.text.MX_GPIO_Init)
    MemManage_Handler                        0x08002091   Thumb Code     2  stm32f4xx_it.o(.text.MemManage_Handler)
    NMI_Handler                              0x08002095   Thumb Code     2  stm32f4xx_it.o(.text.NMI_Handler)
    PendSV_Handler                           0x08002099   Thumb Code     2  stm32f4xx_it.o(.text.PendSV_Handler)
    SVC_Handler                              0x0800209d   Thumb Code     2  stm32f4xx_it.o(.text.SVC_Handler)
    SysTick_Handler                          0x080020a1   Thumb Code     4  stm32f4xx_it.o(.text.SysTick_Handler)
    SystemClock_Config                       0x080020a5   Thumb Code   164  main.o(.text.SystemClock_Config)
    SystemInit                               0x08002149   Thumb Code    18  system_stm32f4xx.o(.text.SystemInit)
    UsageFault_Handler                       0x0800215d   Thumb Code     2  stm32f4xx_it.o(.text.UsageFault_Handler)
    can1send                                 0x08002161   Thumb Code    46  can.o(.text.can1send)
    main                                     0x08002191   Thumb Code    82  main.o(.text.main)
    fputc                                    0x080021e3   Thumb Code    28  fputc.o(i.fputc)
    _fp_init                                 0x080021ff   Thumb Code    26  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08002217   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08002217   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __stdin_name                             0x08002218   Data           4  sys_io_names.o(.constdata)
    __stdout_name                            0x0800221c   Data           4  sys_io_names.o(.constdata)
    __stderr_name                            0x08002220   Data           4  sys_io_names.o(.constdata)
    AHBPrescTable                            0x08002224   Data          16  system_stm32f4xx.o(.rodata.AHBPrescTable)
    Region$$Table$$Base                      0x08002234   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08002254   Number         0  anon$$obj.o(Region$$Table)
    __aeabi_stdin                            0x20000000   Data           4  stdio_streams.o(.data)
    __aeabi_stdout                           0x20000004   Data           4  stdio_streams.o(.data)
    __aeabi_stderr                           0x20000008   Data           4  stdio_streams.o(.data)
    can1id                                   0x2000000c   Data           2  main.o(.data..L_MergedGlobals)
    can1fadata                               0x2000000e   Data           8  main.o(.data..L_MergedGlobals)
    uwTickFreq                               0x20000018   Data           1  stm32f4xx_hal.o(.data..L_MergedGlobals)
    uwTickPrio                               0x2000001c   Data           4  stm32f4xx_hal.o(.data..L_MergedGlobals)
    SystemCoreClock                          0x20000020   Data           4  system_stm32f4xx.o(.data.SystemCoreClock)
    __stdin                                  0x20000028   Data          84  stdio_streams.o(.bss)
    __stdout                                 0x2000007c   Data          84  stdio_streams.o(.bss)
    __stderr                                 0x200000d0   Data          84  stdio_streams.o(.bss)
    __libspace_start                         0x20000124   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000184   Data           0  libspace.o(.bss)
    pTxMailBox                               0x20000184   Data           4  can.o(.bss..L_MergedGlobals)
    rxbuff                                   0x20000188   Data           8  can.o(.bss..L_MergedGlobals)
    can1fa                                   0x20000190   Data          24  can.o(.bss..L_MergedGlobals)
    can2shou                                 0x200001a8   Data          28  can.o(.bss..L_MergedGlobals)
    hcan1                                    0x200001c4   Data          40  can.o(.bss..L_MergedGlobals)
    hcan2                                    0x200001f0   Data          40  can.o(.bss.hcan2)
    uwTick                                   0x20000218   Data           4  stm32f4xx_hal.o(.bss.uwTick)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00002280, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00002254, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000008   Code   RO          583  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x0000005c   Code   RO          935    !!!scatter          c_w.l(__scatter.o)
    0x080001ec   0x080001ec   0x0000001a   Code   RO          939    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000206   0x08000206   0x00000002   PAD
    0x08000208   0x08000208   0x00000002   Code   RO          936    !!handler_null      c_w.l(__scatter.o)
    0x0800020a   0x0800020a   0x00000002   PAD
    0x0800020c   0x0800020c   0x0000001c   Code   RO          941    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000228   0x08000228   0x00000002   Code   RO          734    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800022a   0x0800022a   0x00000004   Code   RO          735    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO          738    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO          740    .ARM.Collect$$libinit$$00000006  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000008   Code   RO          741    .ARM.Collect$$libinit$$00000007  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO          743    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO          745    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO          747    .ARM.Collect$$libinit$$00000010  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO          750    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO          752    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO          754    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO          756    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO          758    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO          760    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO          762    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO          764    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO          766    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO          768    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000004   Code   RO          769    .ARM.Collect$$libinit$$00000026  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO          770    .ARM.Collect$$libinit$$00000027  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO          774    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO          776    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO          778    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO          780    .ARM.Collect$$libinit$$00000034  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000002   Code   RO          781    .ARM.Collect$$libinit$$00000035  c_w.l(libinit2.o)
    0x0800023c   0x0800023c   0x00000002   Code   RO          916    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO          783    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x0800023e   0x0800023e   0x00000004   Code   RO          784    .ARM.Collect$$libshutdown$$00000003  c_w.l(libshutdown2.o)
    0x08000242   0x08000242   0x00000000   Code   RO          785    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000242   0x08000242   0x00000000   Code   RO          788    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000242   0x08000242   0x00000000   Code   RO          791    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000242   0x08000242   0x00000000   Code   RO          793    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000242   0x08000242   0x00000000   Code   RO          796    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000242   0x08000242   0x00000002   Code   RO          797    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000244   0x08000244   0x00000000   Code   RO          585    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000244   0x08000244   0x00000000   Code   RO          603    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000244   0x08000244   0x00000006   Code   RO          615    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800024a   0x0800024a   0x00000000   Code   RO          605    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800024a   0x0800024a   0x00000004   Code   RO          606    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800024e   0x0800024e   0x00000000   Code   RO          608    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800024e   0x0800024e   0x00000008   Code   RO          609    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000256   0x08000256   0x00000002   Code   RO          804    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000258   0x08000258   0x00000000   Code   RO          868    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000258   0x08000258   0x00000004   Code   RO          869    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800025c   0x0800025c   0x00000006   Code   RO          870    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000262   0x08000262   0x00000002   PAD
    0x08000264   0x08000264   0x00000040   Code   RO            4    .text               startup_stm32f407xx.o
    0x080002a4   0x080002a4   0x000000f0   Code   RO          575    .text               c_w.l(lludivv7m.o)
    0x08000394   0x08000394   0x0000000c   Code   RO          577    .text               c_w.l(putchar.o)
    0x080003a0   0x080003a0   0x0000004e   Code   RO          579    .text               c_w.l(rt_memclr_w.o)
    0x080003ee   0x080003ee   0x00000006   Code   RO          581    .text               c_w.l(heapauxi.o)
    0x080003f4   0x080003f4   0x000001e2   Code   RO          619    .text               c_w.l(flsbuf.o)
    0x080005d6   0x080005d6   0x00000002   PAD
    0x080005d8   0x080005d8   0x00000138   Code   RO          621    .text               c_w.l(initio.o)
    0x08000710   0x08000710   0x00000066   Code   RO          627    .text               c_w.l(sys_io.o)
    0x08000776   0x08000776   0x0000004a   Code   RO          636    .text               c_w.l(sys_stackheap_outer.o)
    0x080007c0   0x080007c0   0x0000005e   Code   RO          642    .text               c_w.l(h1_alloc.o)
    0x0800081e   0x0800081e   0x0000004e   Code   RO          644    .text               c_w.l(h1_free.o)
    0x0800086c   0x0800086c   0x00000046   Code   RO          700    .text               c_w.l(setvbuf.o)
    0x080008b2   0x080008b2   0x00000002   PAD
    0x080008b4   0x080008b4   0x00000108   Code   RO          702    .text               c_w.l(stdio.o)
    0x080009bc   0x080009bc   0x000000f0   Code   RO          707    .text               c_w.l(fopen.o)
    0x08000aac   0x08000aac   0x0000004c   Code   RO          709    .text               c_w.l(fclose.o)
    0x08000af8   0x08000af8   0x00000012   Code   RO          717    .text               c_w.l(exit.o)
    0x08000b0a   0x08000b0a   0x0000000e   Code   RO          723    .text               c_w.l(defsig_rtred_outer.o)
    0x08000b18   0x08000b18   0x00000008   Code   RO          798    .text               c_w.l(libspace.o)
    0x08000b20   0x08000b20   0x00000002   Code   RO          801    .text               c_w.l(use_no_semi.o)
    0x08000b22   0x08000b22   0x00000000   Code   RO          803    .text               c_w.l(indicate_semi.o)
    0x08000b22   0x08000b22   0x00000002   PAD
    0x08000b24   0x08000b24   0x00000008   Code   RO          811    .text               c_w.l(rt_heap_descriptor_intlibspace.o)
    0x08000b2c   0x08000b2c   0x00000004   Code   RO          813    .text               c_w.l(hguard.o)
    0x08000b30   0x08000b30   0x0000008a   Code   RO          815    .text               c_w.l(init_alloc.o)
    0x08000bba   0x08000bba   0x0000000c   Code   RO          819    .text               c_w.l(h1_init.o)
    0x08000bc6   0x08000bc6   0x00000002   PAD
    0x08000bc8   0x08000bc8   0x000000f8   Code   RO          835    .text               c_w.l(fseek.o)
    0x08000cc0   0x08000cc0   0x0000000a   Code   RO          841    .text               c_w.l(defsig_exit.o)
    0x08000cca   0x08000cca   0x00000002   PAD
    0x08000ccc   0x08000ccc   0x00000034   Code   RO          845    .text               c_w.l(defsig_rtred_inner.o)
    0x08000d00   0x08000d00   0x0000003e   Code   RO          847    .text               c_w.l(strlen.o)
    0x08000d3e   0x08000d3e   0x00000002   PAD
    0x08000d40   0x08000d40   0x0000000c   Code   RO          861    .text               c_w.l(sys_exit.o)
    0x08000d4c   0x08000d4c   0x00000000   Code   RO          876    .text               c_w.l(maybetermalloc1.o)
    0x08000d4c   0x08000d4c   0x00000034   Code   RO          878    .text               c_w.l(h1_extend.o)
    0x08000d80   0x08000d80   0x00000042   Code   RO          882    .text               c_w.l(ftell.o)
    0x08000dc2   0x08000dc2   0x00000032   Code   RO          886    .text               c_w.l(defsig_general.o)
    0x08000df4   0x08000df4   0x0000000e   Code   RO          888    .text               c_w.l(defsig_rtmem_outer.o)
    0x08000e02   0x08000e02   0x0000000e   Code   RO          899    .text               c_w.l(sys_wrch.o)
    0x08000e10   0x08000e10   0x00000008   Code   RO          906    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000e18   0x08000e18   0x00000050   Code   RO          910    .text               c_w.l(defsig_rtmem_inner.o)
    0x08000e68   0x08000e68   0x00000002   Code   RO           65    .text.BusFault_Handler  stm32f4xx_it.o
    0x08000e6a   0x08000e6a   0x00000002   PAD
    0x08000e6c   0x08000e6c   0x0000000c   Code   RO           77    .text.CAN2_RX0_IRQHandler  stm32f4xx_it.o
    0x08000e78   0x08000e78   0x00000036   Code   RO           38    .text.CAN_FILTER    can.o
    0x08000eae   0x08000eae   0x00000002   PAD
    0x08000eb0   0x08000eb0   0x00000002   Code   RO           71    .text.DebugMon_Handler  stm32f4xx_it.o
    0x08000eb2   0x08000eb2   0x00000002   PAD
    0x08000eb4   0x08000eb4   0x00000006   Code   RO           15    .text.Error_Handler  main.o
    0x08000eba   0x08000eba   0x00000002   PAD
    0x08000ebc   0x08000ebc   0x0000002e   Code   RO          128    .text.HAL_CAN_ActivateNotification  stm32f4xx_hal_can.o
    0x08000eea   0x08000eea   0x00000002   PAD
    0x08000eec   0x08000eec   0x000000a4   Code   RO          114    .text.HAL_CAN_AddTxMessage  stm32f4xx_hal_can.o
    0x08000f90   0x08000f90   0x000000de   Code   RO          104    .text.HAL_CAN_ConfigFilter  stm32f4xx_hal_can.o
    0x0800106e   0x0800106e   0x00000002   PAD
    0x08001070   0x08001070   0x00000002   Code   RO          158    .text.HAL_CAN_ErrorCallback  stm32f4xx_hal_can.o
    0x08001072   0x08001072   0x00000002   PAD
    0x08001074   0x08001074   0x0000012e   Code   RO          124    .text.HAL_CAN_GetRxMessage  stm32f4xx_hal_can.o
    0x080011a2   0x080011a2   0x00000002   PAD
    0x080011a4   0x080011a4   0x0000025e   Code   RO          132    .text.HAL_CAN_IRQHandler  stm32f4xx_hal_can.o
    0x08001402   0x08001402   0x00000002   PAD
    0x08001404   0x08001404   0x000000f4   Code   RO           94    .text.HAL_CAN_Init  stm32f4xx_hal_can.o
    0x080014f8   0x080014f8   0x00000130   Code   RO           40    .text.HAL_CAN_MspInit  can.o
    0x08001628   0x08001628   0x00000002   Code   RO          146    .text.HAL_CAN_RxFifo0FullCallback  stm32f4xx_hal_can.o
    0x0800162a   0x0800162a   0x00000002   PAD
    0x0800162c   0x0800162c   0x00000038   Code   RO           46    .text.HAL_CAN_RxFifo0MsgPendingCallback  can.o
    0x08001664   0x08001664   0x00000002   Code   RO          150    .text.HAL_CAN_RxFifo1FullCallback  stm32f4xx_hal_can.o
    0x08001666   0x08001666   0x00000002   PAD
    0x08001668   0x08001668   0x00000002   Code   RO          152    .text.HAL_CAN_RxFifo1MsgPendingCallback  stm32f4xx_hal_can.o
    0x0800166a   0x0800166a   0x00000002   PAD
    0x0800166c   0x0800166c   0x00000002   Code   RO          154    .text.HAL_CAN_SleepCallback  stm32f4xx_hal_can.o
    0x0800166e   0x0800166e   0x00000002   PAD
    0x08001670   0x08001670   0x0000005a   Code   RO          106    .text.HAL_CAN_Start  stm32f4xx_hal_can.o
    0x080016ca   0x080016ca   0x00000002   PAD
    0x080016cc   0x080016cc   0x00000002   Code   RO          136    .text.HAL_CAN_TxMailbox0AbortCallback  stm32f4xx_hal_can.o
    0x080016ce   0x080016ce   0x00000002   PAD
    0x080016d0   0x080016d0   0x00000002   Code   RO          134    .text.HAL_CAN_TxMailbox0CompleteCallback  stm32f4xx_hal_can.o
    0x080016d2   0x080016d2   0x00000002   PAD
    0x080016d4   0x080016d4   0x00000002   Code   RO          140    .text.HAL_CAN_TxMailbox1AbortCallback  stm32f4xx_hal_can.o
    0x080016d6   0x080016d6   0x00000002   PAD
    0x080016d8   0x080016d8   0x00000002   Code   RO          138    .text.HAL_CAN_TxMailbox1CompleteCallback  stm32f4xx_hal_can.o
    0x080016da   0x080016da   0x00000002   PAD
    0x080016dc   0x080016dc   0x00000002   Code   RO          144    .text.HAL_CAN_TxMailbox2AbortCallback  stm32f4xx_hal_can.o
    0x080016de   0x080016de   0x00000002   PAD
    0x080016e0   0x080016e0   0x00000002   Code   RO          142    .text.HAL_CAN_TxMailbox2CompleteCallback  stm32f4xx_hal_can.o
    0x080016e2   0x080016e2   0x00000002   PAD
    0x080016e4   0x080016e4   0x00000002   Code   RO          156    .text.HAL_CAN_WakeUpFromRxMsgCallback  stm32f4xx_hal_can.o
    0x080016e6   0x080016e6   0x00000002   PAD
    0x080016e8   0x080016e8   0x0000019e   Code   RO          284    .text.HAL_GPIO_Init  stm32f4xx_hal_gpio.o
    0x08001886   0x08001886   0x00000002   PAD
    0x08001888   0x08001888   0x0000000c   Code   RO          481    .text.HAL_GetTick   stm32f4xx_hal.o
    0x08001894   0x08001894   0x0000001a   Code   RO          479    .text.HAL_IncTick   stm32f4xx_hal.o
    0x080018ae   0x080018ae   0x00000002   PAD
    0x080018b0   0x080018b0   0x00000036   Code   RO          469    .text.HAL_Init      stm32f4xx_hal.o
    0x080018e6   0x080018e6   0x00000002   PAD
    0x080018e8   0x080018e8   0x00000048   Code   RO          471    .text.HAL_InitTick  stm32f4xx_hal.o
    0x08001930   0x08001930   0x00000038   Code   RO           86    .text.HAL_MspInit   stm32f4xx_hal_msp.o
    0x08001968   0x08001968   0x00000022   Code   RO          421    .text.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x0800198a   0x0800198a   0x00000002   PAD
    0x0800198c   0x0800198c   0x00000056   Code   RO          419    .text.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080019e2   0x080019e2   0x00000002   PAD
    0x080019e4   0x080019e4   0x00000020   Code   RO          417    .text.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08001a04   0x08001a04   0x00000164   Code   RO          178    .text.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08001b68   0x08001b68   0x0000006e   Code   RO          180    .text.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08001bd6   0x08001bd6   0x00000002   PAD
    0x08001bd8   0x08001bd8   0x000003ac   Code   RO          176    .text.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08001f84   0x08001f84   0x0000002c   Code   RO          429    .text.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08001fb0   0x08001fb0   0x00000002   Code   RO           61    .text.HardFault_Handler  stm32f4xx_it.o
    0x08001fb2   0x08001fb2   0x00000002   PAD
    0x08001fb4   0x08001fb4   0x0000003e   Code   RO           34    .text.MX_CAN1_Init  can.o
    0x08001ff2   0x08001ff2   0x00000002   PAD
    0x08001ff4   0x08001ff4   0x0000003a   Code   RO           36    .text.MX_CAN2_Init  can.o
    0x0800202e   0x0800202e   0x00000002   PAD
    0x08002030   0x08002030   0x00000060   Code   RO           26    .text.MX_GPIO_Init  gpio.o
    0x08002090   0x08002090   0x00000002   Code   RO           63    .text.MemManage_Handler  stm32f4xx_it.o
    0x08002092   0x08002092   0x00000002   PAD
    0x08002094   0x08002094   0x00000002   Code   RO           59    .text.NMI_Handler   stm32f4xx_it.o
    0x08002096   0x08002096   0x00000002   PAD
    0x08002098   0x08002098   0x00000002   Code   RO           73    .text.PendSV_Handler  stm32f4xx_it.o
    0x0800209a   0x0800209a   0x00000002   PAD
    0x0800209c   0x0800209c   0x00000002   Code   RO           69    .text.SVC_Handler   stm32f4xx_it.o
    0x0800209e   0x0800209e   0x00000002   PAD
    0x080020a0   0x080020a0   0x00000004   Code   RO           75    .text.SysTick_Handler  stm32f4xx_it.o
    0x080020a4   0x080020a4   0x000000a4   Code   RO           13    .text.SystemClock_Config  main.o
    0x08002148   0x08002148   0x00000012   Code   RO          559    .text.SystemInit    system_stm32f4xx.o
    0x0800215a   0x0800215a   0x00000002   PAD
    0x0800215c   0x0800215c   0x00000002   Code   RO           67    .text.UsageFault_Handler  stm32f4xx_it.o
    0x0800215e   0x0800215e   0x00000002   PAD
    0x08002160   0x08002160   0x0000002e   Code   RO           44    .text.can1send      can.o
    0x0800218e   0x0800218e   0x00000002   PAD
    0x08002190   0x08002190   0x00000052   Code   RO           11    .text.main          main.o
    0x080021e2   0x080021e2   0x0000001c   Code   RO          591    i.fputc             c_w.l(fputc.o)
    0x080021fe   0x080021fe   0x0000001a   Code   RO          857    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08002218   0x08002218   0x00000004   Data   RO          629    .constdata          c_w.l(sys_io_names.o)
    0x0800221c   0x0800221c   0x00000004   Data   RO          630    .constdata          c_w.l(sys_io_names.o)
    0x08002220   0x08002220   0x00000004   Data   RO          631    .constdata          c_w.l(sys_io_names.o)
    0x08002224   0x08002224   0x00000010   Data   RO          564    .rodata.AHBPrescTable  system_stm32f4xx.o
    0x08002234   0x08002234   0x00000020   Data   RO          934    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08002258, Size: 0x00000820, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08002258   0x00000004   Data   RW          597    .data               c_w.l(stdio_streams.o)
    0x20000004   0x0800225c   0x00000004   Data   RW          598    .data               c_w.l(stdio_streams.o)
    0x20000008   0x08002260   0x00000004   Data   RW          599    .data               c_w.l(stdio_streams.o)
    0x2000000c   0x08002264   0x0000000a   Data   RW           18    .data..L_MergedGlobals  main.o
    0x20000016   0x0800226e   0x00000002   PAD
    0x20000018   0x08002270   0x00000008   Data   RW          524    .data..L_MergedGlobals  stm32f4xx_hal.o
    0x20000020   0x08002278   0x00000004   Data   RW          563    .data.SystemCoreClock  system_stm32f4xx.o
    0x20000024   0x0800227c   0x00000004   PAD
    0x20000028        -       0x00000054   Zero   RW          594    .bss                c_w.l(stdio_streams.o)
    0x2000007c        -       0x00000054   Zero   RW          595    .bss                c_w.l(stdio_streams.o)
    0x200000d0        -       0x00000054   Zero   RW          596    .bss                c_w.l(stdio_streams.o)
    0x20000124        -       0x00000060   Zero   RW          799    .bss                c_w.l(libspace.o)
    0x20000184        -       0x00000068   Zero   RW           50    .bss..L_MergedGlobals  can.o
    0x200001ec        -       0x00000004   Zero   RW           49    .bss.HAL_RCC_CAN1_CLK_ENABLED  can.o
    0x200001f0        -       0x00000028   Zero   RW           48    .bss.hcan2          can.o
    0x20000218        -       0x00000004   Zero   RW          523    .bss.uwTick         stm32f4xx_hal.o
    0x2000021c   0x0800227c   0x00000004   PAD
    0x20000220        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x20000420        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x08002280, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       580          0          0          0        148       8184   can.o
        96          0          0          0          0       1469   gpio.o
       252          0          0         10          0       3314   main.o
        64         26        392          0       1536        836   startup_stm32f407xx.o
       164          0          0          8          4       7295   stm32f4xx_hal.o
      1698          6          0          0          0      14044   stm32f4xx_hal_can.o
       196          0          0          0          0      10713   stm32f4xx_hal_cortex.o
       414          0          0          0          0       5423   stm32f4xx_hal_gpio.o
        56          0          0          0          0       1442   stm32f4xx_hal_msp.o
      1406          4          0          0          0       7565   stm32f4xx_hal_rcc.o
        32          0          0          0          0       3278   stm32f4xx_it.o
        18          0         16          4          0       2584   system_stm32f4xx.o

    ----------------------------------------------------------------------
      5050         <USER>        <GROUP>         24       1696      66147   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        74          0          0          2          8          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        94          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        10          0          0          0          0         68   defsig_exit.o
        50          0          0          0          0         88   defsig_general.o
        80         58          0          0          0         76   defsig_rtmem_inner.o
        14          0          0          0          0         80   defsig_rtmem_outer.o
        52         38          0          0          0         76   defsig_rtred_inner.o
        14          0          0          0          0         80   defsig_rtred_outer.o
        18          0          0          0          0         80   exit.o
        76          0          0          0          0         88   fclose.o
       482          0          0          0          0         92   flsbuf.o
       240          6          0          0          0        128   fopen.o
        28          0          0          0          0         68   fputc.o
       248          6          0          0          0         84   fseek.o
        66          0          0          0          0         76   ftell.o
        94          0          0          0          0         80   h1_alloc.o
        52          0          0          0          0         68   h1_extend.o
        78          0          0          0          0         80   h1_free.o
        12          0          0          0          0         84   h1_init.o
         6          0          0          0          0        152   heapauxi.o
         4          0          0          0          0        136   hguard.o
         0          0          0          0          0          0   indicate_semi.o
       138          0          0          0          0        168   init_alloc.o
       312         46          0          0          0        112   initio.o
         2          0          0          0          0          0   libinit.o
        18          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         6          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       240          0          0          0          0        100   lludivv7m.o
         0          0          0          0          0          0   maybetermalloc1.o
        12          6          0          0          0         68   putchar.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_heap_descriptor_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        70          0          0          0          0         80   setvbuf.o
       264          6          0          0          0        160   stdio.o
         0          0          0         12        252          0   stdio_streams.o
        62          0          0          0          0         76   strlen.o
        12          4          0          0          0         68   sys_exit.o
       102          0          0          0          0        240   sys_io.o
         0          0         12          0          0          0   sys_io_names.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        14          0          0          0          0         76   sys_wrch.o
         2          0          0          0          0         68   use_no_semi.o
        26          0          0          0          0        116   fpinit.o

    ----------------------------------------------------------------------
      3286        <USER>         <GROUP>         12        348       3448   Library Totals
        18          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      3242        190         12         12        348       3332   c_w.l
        26          0          0          0          0        116   fz_wm.l

    ----------------------------------------------------------------------
      3286        <USER>         <GROUP>         12        348       3448   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      8336        226        452         36       2044      67747   Grand Totals
      8336        226        452         36       2044      67747   ELF Image Totals
      8336        226        452         36          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 8788 (   8.58kB)
    Total RW  Size (RW Data + ZI Data)              2080 (   2.03kB)
    Total ROM Size (Code + RO Data + RW Data)       8824 (   8.62kB)

==============================================================================

