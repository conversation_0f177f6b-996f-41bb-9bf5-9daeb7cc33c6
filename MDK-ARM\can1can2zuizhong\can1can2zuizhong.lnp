--cpu=Cortex-M4.fp.sp
"can1can2zuizhong\startup_stm32f407xx.o"
"can1can2zuizhong\main.o"
"can1can2zuizhong\gpio.o"
"can1can2zuizhong\can.o"
"can1can2zuizhong\stm32f4xx_it.o"
"can1can2zuizhong\stm32f4xx_hal_msp.o"
"can1can2zuizhong\stm32f4xx_hal_can.o"
"can1can2zuizhong\stm32f4xx_hal_rcc.o"
"can1can2zuizhong\stm32f4xx_hal_rcc_ex.o"
"can1can2zuizhong\stm32f4xx_hal_flash.o"
"can1can2zuizhong\stm32f4xx_hal_flash_ex.o"
"can1can2zuizhong\stm32f4xx_hal_flash_ramfunc.o"
"can1can2zuizhong\stm32f4xx_hal_gpio.o"
"can1can2zuizhong\stm32f4xx_hal_dma_ex.o"
"can1can2zuizhong\stm32f4xx_hal_dma.o"
"can1can2zuizhong\stm32f4xx_hal_pwr.o"
"can1can2zuizhong\stm32f4xx_hal_pwr_ex.o"
"can1can2zuizhong\stm32f4xx_hal_cortex.o"
"can1can2zuizhong\stm32f4xx_hal.o"
"can1can2zuizhong\stm32f4xx_hal_exti.o"
"can1can2zuizhong\system_stm32f4xx.o"
--strict --scatter "can1can2zuizhong\can1can2zuizhong.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "can1can2zuizhong.map" -o can1can2zuizhong\can1can2zuizhong.axf